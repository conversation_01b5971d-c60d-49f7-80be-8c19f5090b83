import * as React from "react"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

export interface CheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, ...props }, ref) => {
    return (
      <div className="flex items-start space-x-3">
        <div className="relative flex-shrink-0">
          <input
            type="checkbox"
            className={cn(
              "peer h-5 w-5 shrink-0 rounded-md border-2 border-gray-300 shadow-sm transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500/20 focus-visible:border-indigo-500 hover:border-indigo-400 disabled:cursor-not-allowed disabled:opacity-50 checked:bg-gradient-to-r checked:from-indigo-600 checked:to-purple-600 checked:border-transparent",
              className
            )}
            ref={ref}
            {...props}
          />
          <Check className="absolute left-0.5 top-0.5 h-3 w-3 text-white opacity-0 peer-checked:opacity-100 pointer-events-none transition-opacity duration-200" />
        </div>
        {label && (
          <label
            htmlFor={props.id}
            className="text-sm font-medium leading-relaxed text-gray-700 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
          >
            {label}
          </label>
        )}
      </div>
    )
  }
)
Checkbox.displayName = "Checkbox"

export { Checkbox }
