"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, onAuthStateChanged } from 'firebase/auth'
import { auth } from './firebase'
import { getUserDocument, UserDocument } from './firestore'

interface AuthContextType {
  user: User | null
  userDocument: UserDocument | null
  loading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userDocument: null,
  loading: true,
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [userDocument, setUserDocument] = useState<UserDocument | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)

      if (user) {
        try {
          // Fetch user document from Firestore
          const userDoc = await getUserDocument(user.uid)
          setUserDocument(userDoc)
        } catch (error) {
          console.error('Error fetching user document:', error)
          setUserDocument(null)
        }
      } else {
        setUserDocument(null)
      }

      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  const value = {
    user,
    userDocument,
    loading,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
