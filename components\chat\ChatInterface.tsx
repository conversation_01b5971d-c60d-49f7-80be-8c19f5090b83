"use client"

import { useState, useCallback } from "react"
import { Message } from "@/types/chat"
import { MessageList } from "./MessageList"
import { ChatInput } from "./ChatInput"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Settings, MoreVertical, Plus, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

export function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Generate a unique ID for messages
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Simulate AI response (replace with actual API call)
  const simulateAIResponse = useCallback(async (userMessage: string): Promise<string> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    // Simple response simulation
    const responses = [
      "I understand your question. Let me help you with that.",
      "That's an interesting point. Here's what I think about it...",
      "I'd be happy to assist you with this. Based on what you've shared...",
      "Great question! Let me break this down for you.",
      "I can help you with that. Here's my analysis of the situation..."
    ]
    
    return responses[Math.floor(Math.random() * responses.length)] + 
           ` You mentioned: "${userMessage}". This is a thoughtful question that requires careful consideration.`
  }, [])

  const handleSendMessage = useCallback(async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: generateMessageId(),
      content,
      role: 'user',
      timestamp: new Date(),
      status: 'sent'
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Simulate AI response
      const aiResponse = await simulateAIResponse(content)
      
      // Add AI message
      const aiMessage: Message = {
        id: generateMessageId(),
        content: aiResponse,
        role: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      // Handle error
      const errorMessage: Message = {
        id: generateMessageId(),
        content: "I apologize, but I encountered an error while processing your request. Please try again.",
        role: 'assistant',
        timestamp: new Date(),
        status: 'error'
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }, [simulateAIResponse])

  const handleNewChat = () => {
    setMessages([])
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">AI Chat</h1>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs">
                  GPT-4
                </Badge>
                <span className="text-xs text-gray-500">Ready to help</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewChat}
              className="text-gray-600 hover:text-gray-900"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Chat
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-600 hover:text-gray-900"
            >
              <Settings className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-600 hover:text-gray-900"
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Messages Area */}
      <MessageList 
        messages={messages} 
        isLoading={isLoading}
      />

      {/* Input Area */}
      <ChatInput 
        onSendMessage={handleSendMessage}
        disabled={isLoading}
        placeholder="Ask me anything..."
      />
    </div>
  )
}
