import { doc, setDoc, getDoc, serverTimestamp, Timestamp } from 'firebase/firestore'
import { db } from './firebase'
import { User } from 'firebase/auth'

// Available user tiers
export const USER_TIERS = {
  STARTER: 'Starter',
  PRO: 'Pro',
  BUSINESS: 'Business',
  PREMIUM: 'Premium'
} as const

export type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS]

// User document interface
export interface UserDocument {
  uid: string
  fullName: string
  email: string
  tier: UserTier
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Create user document in Firestore
export const createUserDocument = async (
  user: User,
  fullName: string
): Promise<void> => {
  try {
    const userDocRef = doc(db, 'users', user.uid)

    // Check if user document already exists
    const userDocSnap = await getDoc(userDocRef)

    if (!userDocSnap.exists()) {
      const userData: Omit<UserDocument, 'createdAt' | 'updatedAt'> & {
        createdAt: any
        updatedAt: any
      } = {
        uid: user.uid,
        fullName: fullName,
        email: user.email || '',
        tier: USER_TIERS.STARTER, // Default tier for new users
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      await setDoc(userDocRef, userData)
      console.log('User document created successfully')
    } else {
      console.log('User document already exists')
    }
  } catch (error) {
    console.error('Error creating user document:', error)
    throw new Error('Failed to create user profile')
  }
}

// Get user document from Firestore
export const getUserDocument = async (uid: string): Promise<UserDocument | null> => {
  try {
    const userDocRef = doc(db, 'users', uid)
    const userDocSnap = await getDoc(userDocRef)

    if (userDocSnap.exists()) {
      return userDocSnap.data() as UserDocument
    } else {
      return null
    }
  } catch (error) {
    console.error('Error getting user document:', error)
    throw new Error('Failed to get user profile')
  }
}

// Update user document in Firestore
export const updateUserDocument = async (
  uid: string,
  updates: Partial<Omit<UserDocument, 'uid' | 'createdAt'>>
): Promise<void> => {
  try {
    const userDocRef = doc(db, 'users', uid)

    const updateData = {
      ...updates,
      updatedAt: serverTimestamp(),
    }

    await setDoc(userDocRef, updateData, { merge: true })
    console.log('User document updated successfully')
  } catch (error) {
    console.error('Error updating user document:', error)
    throw new Error('Failed to update user profile')
  }
}

// Update user tier specifically
export const updateUserTier = async (uid: string, newTier: UserTier): Promise<void> => {
  try {
    await updateUserDocument(uid, { tier: newTier })
    console.log(`User tier updated to: ${newTier}`)
  } catch (error) {
    console.error('Error updating user tier:', error)
    throw new Error('Failed to update user tier')
  }
}

// Check if user has access to a specific tier or higher
export const hasAccessToTier = (userTier: UserTier, requiredTier: UserTier): boolean => {
  const tierHierarchy = [
    USER_TIERS.STARTER,
    USER_TIERS.PRO,
    USER_TIERS.BUSINESS,
    USER_TIERS.PREMIUM
  ]

  const userTierIndex = tierHierarchy.indexOf(userTier)
  const requiredTierIndex = tierHierarchy.indexOf(requiredTier)

  return userTierIndex >= requiredTierIndex
}
