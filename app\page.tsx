"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MessageCircle, ImageIcon, Mic, Sparkles, Check, ArrowRight, Menu, X, LogOut } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/lib/auth-context"
import { logOut } from "@/lib/auth"

export default function LandingPage() {
  const { user, loading } = useAuth()
  const [activeSection, setActiveSection] = useState(0)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [typedText, setTypedText] = useState("")
  const fullText = "Infinite AI power."

  // Handle logout
  const handleLogout = async () => {
    try {
      await logOut()
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }

  // Refs for scroll animations
  const featuresRef = useRef<HTMLDivElement>(null)
  const pricingRef = useRef<HTMLDivElement>(null)
  const companiesRef = useRef<HTMLDivElement>(null)

  // Typing animation effect with delete and rewrite
  useEffect(() => {
    let currentIndex = 0
    let isDeleting = false
    let isPaused = false

    const typingInterval = setInterval(() => {
      if (!isDeleting && !isPaused) {
        // Typing
        if (currentIndex < fullText.length) {
          setTypedText(fullText.slice(0, currentIndex + 1))
          currentIndex++
        } else {
          // Finished typing, pause before deleting
          isPaused = true
          setTimeout(() => {
            isPaused = false
            isDeleting = true
          }, 2000) // Pause for 2 seconds after typing
        }
      } else if (isDeleting && !isPaused) {
        // Deleting
        if (currentIndex > 0) {
          setTypedText(fullText.slice(0, currentIndex - 1))
          currentIndex--
        } else {
          // Finished deleting, pause before retyping
          isPaused = true
          setTimeout(() => {
            isPaused = false
            isDeleting = false
          }, 500) // Short pause before retyping
        }
      }
    }, isDeleting ? 50 : 100) // Faster deletion speed

    return () => clearInterval(typingInterval)
  }, [])

  const features = [
    {
      icon: MessageCircle,
      title: "AI Chat Hub",
      description: "Access top chatbots (GPT, Claude, Gemini)",
      details: [
        "Choose which model to talk to",
        "Simple interface for chatting and questions",
        "Switch models on the fly",
        "File upload support (PDF, DOCX, etc.)",
      ],
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: ImageIcon,
      title: "AI Media Generation",
      description: "Generate images, videos, and audio from text",
      details: [
        "Support for Imagen, Veo, Lyria, and more",
        "Gallery UI to display generated content",
        "Easy-to-use prompt box with presets",

      ],
      gradient: "from-purple-500 to-pink-500",
    },
    {
      icon: Mic,
      title: "Voice Chat with AI",
      description: "Talk directly with AI models using your mic",
      details: [
        "Real-time speech-to-text and text-to-speech",
        "Selectable voices (male/female, languages)",
        "Perfect for accessibility and hands-free use",
        "Great for practicing languages",
      ],
      gradient: "from-indigo-500 to-purple-500",
      comingSoon: true,
    },
  ]

  const pricingPlans = [
    {
      name: "Starter",
      price: "Free",
      description: "Perfect for trying out AI tools",
      credits: "100 credits/month",
      features: ["Access to basic AI models", "Text chat functionality", "Basic image generation"],
    },
    {
      name: "Pro",
      price: "$19",
      description: "For power users and professionals",
      credits: "2,000 credits/month",
      features: [
        "Access to most AI models",
        "Voice chat capabilities",
        "Full image and basic video generation",
        "Priority support",
        "File upload (up to 50MB)",
        "Voice chat (1 hour/month)",
      ],
      popular: true,
    },
        {
      name: "Business",
      price: "$19",
      description: "For power users and professionals",
      credits: "5,000 credits/month",
      features: [
        "All Pro features",
        "Access To All AI Models",
        "Full video and audio generation",
        "Voice chat (5 hours/month) with multi-language voices",
        "Priority support",
        "File upload (up to 50MB)",
        "More Monthly Credits",
      ],
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveSection((prev) => (prev + 1) % 3)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  // Intersection Observer for scroll animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -100px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in-up')
        }
      })
    }, observerOptions)

    const sections = [featuresRef.current, pricingRef.current, companiesRef.current]
    sections.forEach(section => {
      if (section) observer.observe(section)
    })

    return () => {
      sections.forEach(section => {
        if (section) observer.unobserve(section)
      })
    }
  }, [])

  return (
    <div className="min-h-screen bg-white">
      {/* Add custom styles for animations */}
      <style jsx global>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }

        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }

        @keyframes pulse-scale {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        .animate-fade-in {
          animation: fade-in-up 0.8s ease-out forwards;
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.8s ease-out forwards;
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient-shift 8s ease infinite;
        }

        .animate-pulse-scale {
          animation: pulse-scale 2s ease-in-out infinite;
        }

        .hover-lift {
          transition: all 0.3s ease;
        }

        .hover-lift:hover {
          transform: translateY(-5px) scale(1.02);
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .typing-cursor::after {
          content: '|';
          animation: blink 1s infinite;
          margin-left: 2px;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        .parallax-bg {
          transform: translateY(var(--parallax-y, 0));
          transition: transform 0.5s ease-out;
        }
      `}</style>

      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-100 z-50 animate-fade-in">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Promptly</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/Models" className="text-gray-600 hover:text-gray-900 transition-colors">
                Models
              </Link>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                Pricing
              </a>
              {!loading && (
                <>
                  {user ? (
                    // Authenticated user navigation
                    <>
                      <Link href="/dashboard">
                        <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Dashboard
                        </Button>
                      </Link>
                      <Button variant="ghost" onClick={handleLogout}>
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                      </Button>
                    </>
                  ) : (
                    // Non-authenticated user navigation
                    <>
                      <Link href="/SignInPage">
                        <Button variant="ghost">Login</Button>
                      </Link>
                      <Link href="/SignUpPage">
                        <Button>Sign Up</Button>
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="sm" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
                {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-100">
            <div className="px-4 py-2 space-y-2">
              <Link href="/Models" className="block py-2 text-gray-600">
                Models
              </Link>
              <a href="#pricing" className="block py-2 text-gray-600">
                Pricing
              </a>
              {!loading && (
                <>
                  {user ? (
                    // Authenticated user mobile navigation
                    <div className="space-y-2 pt-2">
                      <Link href="/dashboard" className="block">
                        <Button size="sm" className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Dashboard
                        </Button>
                      </Link>
                      <Button variant="ghost" size="sm" className="w-full" onClick={handleLogout}>
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                      </Button>
                    </div>
                  ) : (
                    // Non-authenticated user mobile navigation
                    <div className="flex space-x-2 pt-2">
                      <Link href="/SignInPage" className="flex-1">
                        <Button variant="ghost" size="sm" className="w-full">
                          Login
                        </Button>
                      </Link>
                      <Link href="/SignUpPage" className="flex-1">
                        <Button size="sm" className="w-full">
                          Sign Up
                        </Button>
                      </Link>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 animate-gradient" />
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-100/20 to-purple-100/20" />
          <div
            className="absolute inset-0 parallax-bg"
            style={{
              backgroundImage: "radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)",
              backgroundSize: "20px 20px",
            }}
          />
        </div>

        {/* Floating background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="max-w-7xl mx-auto relative">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 animate-fade-in">
              One platform.{" "}
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent typing-cursor">
                {typedText}
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto animate-fade-in" style={{ animationDelay: '0.2s' }}>
              Text, voice, and media tools powered by the world's top AI models. Access ChatGPT, Claude, Gemini, and
              more in one unified platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in" style={{ animationDelay: '0.4s' }}>
              {!loading && (
                <>
                  {user ? (
                    // Authenticated user - show chat button
                    <Link href="/dashboard">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 hover-lift animate-pulse-scale"
                      >
                        <MessageCircle className="mr-2 w-5 h-5" />
                        Go to Dashboard
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  ) : (
                    // Non-authenticated user - show sign up button
                    <Link href="/SignUpPage">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 hover-lift animate-pulse-scale"
                      >
                        Start for Free
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  )}
                </>
              )}
              <Button
                size="lg"
                variant="outline"
                className="hover-lift"
                onClick={() => {
                  const pricingSection = document.getElementById('pricing');
                  if (pricingSection) {
                    pricingSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                See Plans
              </Button>
            </div>
          </div>

          {/* Animated Feature Preview */}
          <div className="mt-16 relative animate-fade-in" style={{ animationDelay: '0.6s' }}>
            <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
              {features.map((feature, index) => (
                <Card
                  key={index}
                  className={`transition-all duration-500 cursor-pointer hover-lift ${
                    activeSection === index
                      ? "scale-105 shadow-xl border-transparent"
                      : "hover:scale-102 hover:shadow-lg"
                  }`}
                  style={{
                    background:
                      activeSection === index
                        ? `linear-gradient(135deg, ${feature.gradient.includes("blue") ? "#3B82F6" : feature.gradient.includes("purple") ? "#8B5CF6" : "#6366F1"}, ${feature.gradient.includes("cyan") ? "#06B6D4" : feature.gradient.includes("pink") ? "#EC4899" : "#8B5CF6"})`
                        : "white",
                    animationDelay: `${0.8 + index * 0.1}s`
                  }}
                  onClick={() => setActiveSection(index)}
                >
                  <CardHeader className="text-center">
                    <div
                      className={`w-12 h-12 mx-auto rounded-lg flex items-center justify-center mb-4 ${
                        activeSection === index ? "bg-white/20" : "bg-gradient-to-r " + feature.gradient
                      } ${activeSection === index ? 'animate-pulse-scale' : ''}`}
                    >
                      <feature.icon className={`w-6 h-6 ${activeSection === index ? "text-white" : "text-white"}`} />
                    </div>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <CardTitle className={activeSection === index ? "text-white" : "text-gray-900"}>
                        {feature.title}
                      </CardTitle>
                      {feature.comingSoon && (
                        <Badge
                          variant="secondary"
                          className={`text-xs ${
                            activeSection === index
                              ? "bg-white/20 text-white border-white/30"
                              : "bg-orange-100 text-orange-800 border-orange-200"
                          }`}
                        >
                          Coming Soon
                        </Badge>
                      )}
                    </div>
                    <CardDescription className={activeSection === index ? "text-white/80" : "text-gray-600"}>
                      {feature.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Feature Sections */}
      <section ref={featuresRef} className="py-16 px-4 sm:px-6 lg:px-8 opacity-0">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-12">
            {features.map((feature, index) => (
              <div key={index} id={feature.title.toLowerCase().replace(" ", "-")}>
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift">
                  <CardHeader>
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center mb-4 animate-gradient`}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="flex items-center gap-3 mb-2">
                      <CardTitle className="text-2xl">{feature.title}</CardTitle>
                      {feature.comingSoon && (
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
                          Coming Soon
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="text-lg">{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {feature.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-600">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Companies Section */}
      <section ref={companiesRef} className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-gray-50 overflow-hidden relative opacity-0">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Models From The Biggest AI Companies
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Access cutting-edge AI models from the world's leading technology companies
            </p>
          </div>

          {/* Animated Logo Rows */}
          <div className="relative h-32 mb-8">
            {/* First row - moving right */}
            <div className="absolute top-0 left-0 w-full h-16 flex items-center">
              <div className="animate-[float-right_30s_linear_infinite] flex items-center space-x-16">
                {/* Google Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Gemini-Logo_mgs7kf"
                    alt="Google"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* OpenAI Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/OpenAi-Logo_kkytmp"
                    alt="OpenAI"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Anthropic Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Anthropic-Logo_fctjdy"
                    alt="Anthropic"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Meta Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Meta-Logo_tfkxs2"
                    alt="Meta"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Repeat for continuous effect */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Gemini-Logo_mgs7kf"
                    alt="Google"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/OpenAi-Logo_kkytmp"
                    alt="OpenAI"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Anthropic-Logo_fctjdy"
                    alt="Anthropic"
                    className="w-24 h-12 object-contain"
                  />
                </div>
              </div>
            </div>

            {/* Second row - moving left */}
            <div className="absolute bottom-0 right-0 w-full h-16 flex items-center justify-end">
              <div className="animate-[float-left_25s_linear_infinite] flex items-center space-x-16">
                {/* Microsoft Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/DeepSeek-Logo_jhizcb"
                    alt="Microsoft"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Cohere Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Xai-Logo_wl0csn"
                    alt="Cohere"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Stability AI Logo */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/mistral-color_zju4jp"
                    alt="Stability AI"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                {/* Repeat for continuous effect */}
                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/DeepSeek-Logo_jhizcb"
                    alt="Microsoft"
                    className="w-24 h-12 object-contain"
                  />
                </div>

                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/Xai-Logo_wl0csn"
                    alt="Cohere"
                    className="w-24 h-12 object-contain"
                  />
                </div>
                                <div className="flex-shrink-0 bg-white rounded-lg shadow-md p-4 border border-gray-100">
                  <img
                    src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/v1/AI%20LOGOS/mistral-color_zju4jp"
                    alt="Cohere"
                    className="w-24 h-12 object-contain"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Text */}
          <div className="text-center">
            <p className="text-gray-500 text-sm">
              And many more cutting-edge AI models available on our platform
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section ref={pricingRef} id="pricing" className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 opacity-0">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Simple, transparent pricing</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose the plan that fits your needs. All plans include access to our core AI models.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`relative transition-all duration-300 hover-lift ${plan.popular ? "border-indigo-500 shadow-xl scale-105" : "border-gray-200"}`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-indigo-600 to-purple-600">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    {plan.price !== "Free" && plan.price !== "Custom" && <span className="text-gray-600">/month</span>}
                  </div>
                  <CardDescription className="mt-2">{plan.description}</CardDescription>
                  <div className="mt-4">
                    <Badge variant="secondary">{plan.credits}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-3">
                        <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  {!loading && (
                    <>
                      {user ? (
                        // Authenticated user - show chat button for all plans
                        <Link href="/dashboard">
                          <Button
                            className={`w-full ${plan.popular ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700" : ""}`}
                            variant={plan.popular ? "default" : "outline"}
                          >
                            <MessageCircle className="w-4 h-4 mr-2" />
                            Go to Dashboard
                          </Button>
                        </Link>
                      ) : (
                        // Non-authenticated user - show original buttons
                        <Link href={plan.price === "Custom" ? "/contact" : "/SignUpPage"}>
                          <Button
                            className={`w-full ${plan.popular ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700" : ""}`}
                            variant={plan.popular ? "default" : "outline"}
                          >
                            {plan.price === "Custom" ? "Contact Sales" : "Get Started"}
                          </Button>
                        </Link>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">Promptly</span>
              </div>
              <p className="text-gray-400 mb-4">
                One platform for all your AI needs. Built with the world's leading AI models.
              </p>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Promptly. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
